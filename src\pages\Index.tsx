
import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, useParams, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/hooks/useSubscription';
import ChatSidebar from '@/components/ChatSidebar';
import ChatInterface from '@/components/ChatInterface';
import { ExternalLink, User, LogOut, Settings, FolderOpen, Plus, CreditCard, BarChart3 } from 'lucide-react';
import Logo from '@/components/Logo';
import BackgroundEffects from '@/components/BackgroundEffects';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Link } from 'react-router-dom';
import Footer from '@/components/Footer';
import PromptInput from '@/components/PromptInput';
import PromptOutput from '@/components/PromptOutput';
import PromptStyleSelector from '@/components/PromptStyleSelector';
import { generatePrompt } from '@/utils/generatePrompt';
import { ChatService } from '@/lib/chatService';
import { GuestUsageService } from '@/lib/guestUsageService';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';
import Header from '@/components/Header';

const Index: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const params = useParams<{ chatId?: string; projectId?: string }>();
  const location = useLocation();
  const { isAuthenticated, user, profile, signOut, isAdmin } = useAuth();
  const { canPerformAction, incrementUsage } = useSubscription();

  // Chat interface state (for authenticated users)
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [currentChatId, setCurrentChatId] = useState<string | undefined>();
  const [currentProjectId, setCurrentProjectId] = useState<string | undefined>();
  const [showNewProjectDialog, setShowNewProjectDialog] = useState(false);
  const [sidebarKey, setSidebarKey] = useState(0);
  const [newProjectForm, setNewProjectForm] = useState({
    name: '',
    description: '',
    customInstructions: ''
  });
  const [creatingProject, setCreatingProject] = useState(false);

  // Landing page state (for non-authenticated users)
  const [userIdea, setUserIdea] = useState('');
  const [selectedStyle, setSelectedStyle] = useState('technical');
  const [generatedPrompt, setGeneratedPrompt] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);

  // Guest usage state
  const [showSignupModal, setShowSignupModal] = useState(false);
  const [guestUsageExceeded, setGuestUsageExceeded] = useState(false);
  const [remainingFreePrompts, setRemainingFreePrompts] = useState(1);

  // Handle URL parameters for chat/project navigation (both legacy routes and query params)
  useEffect(() => {
    if (isAuthenticated) {
      // Check for legacy route parameters first
      const legacyChatId = params.chatId;
      const legacyProjectId = params.projectId;

      // Check for query parameters
      const queryChatId = searchParams.get('chat');
      const queryProjectId = searchParams.get('project');

      // Use legacy route params if available, otherwise use query params
      const finalChatId = legacyChatId || queryChatId;
      const finalProjectId = legacyProjectId || queryProjectId;

      setCurrentChatId(finalChatId || undefined);
      setCurrentProjectId(finalProjectId || undefined);

      // If we have legacy route params, redirect to homepage with query params
      if (legacyChatId || legacyProjectId) {
        const newSearchParams = new URLSearchParams();
        if (legacyChatId) newSearchParams.set('chat', legacyChatId);
        if (legacyProjectId) newSearchParams.set('project', legacyProjectId);

        navigate(`/?${newSearchParams.toString()}`, { replace: true });
      }
    }
  }, [searchParams, params, isAuthenticated, navigate]);

  // Check guest usage on component mount for non-authenticated users
  useEffect(() => {
    if (!isAuthenticated) {
      const checkGuestUsage = async () => {
        const { remaining, error } = await GuestUsageService.getRemainingPrompts();
        if (!error) {
          setRemainingFreePrompts(remaining);
          setGuestUsageExceeded(remaining === 0);
        }
      };

      checkGuestUsage();
    }
  }, [isAuthenticated]);

  // Chat interface handlers (for authenticated users)
  const handleChatSelect = (selectedChatId: string) => {
    setCurrentChatId(selectedChatId);
    setSearchParams({ chat: selectedChatId });
  };

  const handleNewChat = () => {
    setCurrentChatId(undefined);
    setCurrentProjectId(undefined);
    setSearchParams({});
  };

  const handleNewProject = () => {
    setShowNewProjectDialog(true);
  };

  const handleChatCreated = (newChatId: string) => {
    setCurrentChatId(newChatId);
    setSearchParams({ chat: newChatId });
    setSidebarKey(prev => prev + 1);
  };

  const createProject = async () => {
    if (!newProjectForm.name.trim()) {
      toast.error('Project name is required');
      return;
    }

    setCreatingProject(true);

    try {
      const { project, error } = await ChatService.createProject({
        name: newProjectForm.name.trim(),
        description: newProjectForm.description.trim() || undefined,
        customInstructions: newProjectForm.customInstructions.trim() || undefined
      });

      if (error || !project) {
        toast.error('Failed to create project');
        return;
      }

      toast.success('Project created successfully');
      setShowNewProjectDialog(false);
      setNewProjectForm({ name: '', description: '', customInstructions: '' });

      // Navigate to the new project
      setCurrentProjectId(project.id);
      setCurrentChatId(undefined);
      setSearchParams({ project: project.id });
      setSidebarKey(prev => prev + 1);
    } catch (error) {
      console.error('Error creating project:', error);
      toast.error('Failed to create project');
    } finally {
      setCreatingProject(false);
    }
  };

  // Check if user has premium access
  const hasPremiumAccess = () => {
    console.log('Debug premium access:', {
      isAuthenticated,
      profile,
      subscription_status: profile?.subscription_status,
      user_email: user?.email
    });
    
    return isAuthenticated && (
      profile?.subscription_status === 'premium' ||
      profile?.subscription_status === 'admin' ||
      user?.email === '<EMAIL>' // Admin override
    );
  };

  // Landing page handlers (for non-authenticated users and guests)
  const handleGeneratePrompt = async () => {
    if (!userIdea.trim()) {
      toast.error('Please enter an idea first');
      return;
    }

    // Handle authenticated users with premium access
    if (isAuthenticated && hasPremiumAccess()) {
      // Check usage limits for authenticated premium users
      const canGenerate = await canPerformAction('prompt');
      if (!canGenerate) {
        return; // Error message is shown by canPerformAction
      }
    }
    // Handle authenticated users without premium access
    else if (isAuthenticated && !hasPremiumAccess()) {
      toast.error('Premium subscription required to generate prompts', {
        action: {
          label: 'Subscribe Now',
          onClick: () => navigate('/pricing')
        }
      });
      return;
    }
    // Handle non-authenticated users (guests) - allow one free prompt
    else if (!isAuthenticated) {
      // Check if guest can use free trial
      const { canUse, error } = await GuestUsageService.canUseFreeTrial();

      if (error) {
        console.error('Error checking guest usage:', error);
        toast.error('Unable to verify usage. Please try again.');
        return;
      }

      if (!canUse) {
        // Show signup modal instead of generating
        setShowSignupModal(true);
        return;
      }
    }

    setIsGenerating(true);
    try {
      const prompt = await generatePrompt(userIdea, selectedStyle);
      setGeneratedPrompt(prompt);

      // Save to chat history if user is authenticated
      if (isAuthenticated && prompt) {
        try {
          // Create a new chat for this prompt generation
          const { chat: newChat, error: chatError } = await ChatService.createChat({
            title: userIdea.substring(0, 50), // Use the user's idea as the title
          });

          if (chatError || !newChat) {
            console.error('Failed to create chat for prompt history:', chatError);
          } else {
            // Save the user's input as a user message
            const { error: userMessageError } = await ChatService.sendMessage({
              chatId: newChat.id,
              content: userIdea,
              role: 'user'
            });

            if (userMessageError) {
              console.error('Failed to save user message:', userMessageError);
            }

            // Save the generated prompt as an assistant message
            const { error: assistantMessageError } = await ChatService.sendMessage({
              chatId: newChat.id,
              content: prompt,
              role: 'assistant',
              metadata: {
                promptStyle: selectedStyle,
                generatedFromHomepage: true
              }
            });

            if (assistantMessageError) {
              console.error('Failed to save assistant message:', assistantMessageError);
            }

            // Refresh the sidebar to show the new chat
            setSidebarKey(prev => prev + 1);

            console.log('Prompt conversation saved to chat history:', newChat.id);
          }
        } catch (error) {
          console.error('Error saving prompt to chat history:', error);
          // Don't show error to user since the main functionality (prompt generation) worked
        }
      }

      // Handle usage tracking
      if (isAuthenticated) {
        // Increment usage for authenticated users
        await incrementUsage('prompt');
        await incrementUsage('api_call'); // Also count API usage
      } else {
        // Increment guest usage and show appropriate message
        const { success, error } = await GuestUsageService.incrementUsage();

        if (error) {
          console.error('Error tracking guest usage:', error);
        }

        if (success) {
          // Update local state
          setRemainingFreePrompts(0);
          setGuestUsageExceeded(true);

          // Show success message with signup encouragement
          GuestUsageService.showFreeTrialMessage();

          // Show signup modal after a short delay
          setTimeout(() => {
            setShowSignupModal(true);
          }, 2000);
        }
      }
    } catch (error) {
      console.error('Error generating prompt:', error);
      toast.error('Failed to generate prompt');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleStyleChange = (style: string) => {
    setSelectedStyle(style);
  };

  const handleProjectSaved = (projectId: string) => {
    // Handle project saved callback if needed
    console.log('Project saved:', projectId);
  };

  // Render the same homepage UI for both authenticated and non-authenticated users
  return (
    <div className="min-h-screen relative">
      <BackgroundEffects />

      <div className="relative z-10">
        {/* Sidebar - only show when authenticated */}
        {isAuthenticated && (
          <ChatSidebar
            key={sidebarKey}
            isCollapsed={sidebarCollapsed}
            onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed)}
            currentChatId={currentChatId}
            onChatSelect={handleChatSelect}
            onNewChat={handleNewChat}
            onNewProject={handleNewProject}
          />
        )}

        {/* Main Content */}
        <div className={`min-h-screen overflow-y-auto transition-all duration-200 ease-linear ${
          isAuthenticated
            ? sidebarCollapsed
              ? 'ml-12'
              : 'ml-64'
            : ''
        }`}>
          {/* Show ChatInterface if user is authenticated and has selected a chat or project */}
          {isAuthenticated && (currentChatId || currentProjectId) ? (
            <ChatInterface
              chatId={currentChatId}
              projectId={currentProjectId}
              onChatCreated={handleChatCreated}
            />
          ) : (
            <>
              <Header />
              <main className="container max-w-3xl mx-auto px-6 mt-16 text-center">
                <h1 className="text-2xl md:text-5xl font-bold tracking-tight animate-fade-in">
                  Transform Your <span className="text-highlight">Ideas</span><br />
                  into Perfect <span className="text-highlight">AI Prompts</span>
                </h1>

                <p className="mt-6 text-xl text-gray-600 animate-fade-in" style={{ animationDelay: '0.1s' }}>
                  Craft powerful, context-rich prompts for any AI model in seconds.
                  Enhance your AI interactions with professionally engineered prompts.
                </p>

                <div className="mt-12 animate-fade-in" style={{ animationDelay: '0.2s' }}>
                  <div className="flex flex-col gap-4">
                    <PromptStyleSelector
                      value={selectedStyle}
                      onChange={handleStyleChange}
                    />

                    <PromptInput
                      value={userIdea}
                      onChange={setUserIdea}
                      onSubmit={handleGeneratePrompt}
                      isDisabled={isGenerating || (!isAuthenticated && guestUsageExceeded)}
                    />

                    {/* Usage exceeded message for guests */}
                    {!isAuthenticated && guestUsageExceeded && (
                      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <p className="text-blue-700 text-center">
                          🚀 Ready for unlimited prompts?
                          <Button
                            onClick={() => navigate('/auth')}
                            variant="link"
                            className="text-blue-600 hover:text-blue-800 p-0 ml-1 h-auto"
                          >
                            Sign up now
                          </Button>
                        </p>
                      </div>
                    )}

                    {!generatedPrompt ? (
                      <p className="mt-4 text-gray-600 text-sm animate-fade-in" style={{ animationDelay: '0.4s' }}>
                        Your AI-optimized prompt will appear here
                      </p>
                    ) : null}
                  </div>
                </div>

                <PromptOutput
                  content={generatedPrompt}
                  userIdea={userIdea}
                  selectedStyle={selectedStyle}
                  onProjectSaved={handleProjectSaved}
                />
              </main>
              <Footer />
            </>
          )}
        </div>
      </div>

      {/* New Project Dialog - only show when authenticated */}
      {isAuthenticated && (
        <Dialog open={showNewProjectDialog} onOpenChange={setShowNewProjectDialog}>
          <DialogContent className="glass-panel backdrop-blur-sm border-0 shadow-2xl max-w-md">
            <DialogHeader className="text-center pb-6">
              <div className="w-12 h-12 bg-gradient-to-tr from-highlight to-blue-400 rounded-full flex items-center justify-center mx-auto mb-4">
                <FolderOpen className="w-6 h-6 text-white" />
              </div>
              <DialogTitle className="text-2xl font-bold text-gray-900">Create New Project</DialogTitle>
              <DialogDescription className="text-gray-600 mt-2">
                Organize your chats and files in a dedicated project workspace.
              </DialogDescription>
            </DialogHeader>

            <div className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="project-name" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <FolderOpen className="w-4 h-4" />
                  Project Name
                </Label>
                <Input
                  id="project-name"
                  value={newProjectForm.name}
                  onChange={(e) => setNewProjectForm(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="My Awesome Project"
                  className="bg-white/50 backdrop-blur-sm border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-xl px-4 py-3 transition-all"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="project-description" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <Settings className="w-4 h-4" />
                  Description <span className="text-gray-400 text-xs">(Optional)</span>
                </Label>
                <Input
                  id="project-description"
                  value={newProjectForm.description}
                  onChange={(e) => setNewProjectForm(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Brief description of your project"
                  className="bg-white/50 backdrop-blur-sm border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-xl px-4 py-3 transition-all"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="project-instructions" className="text-sm font-medium text-gray-700 flex items-center gap-2">
                  <User className="w-4 h-4" />
                  Custom Instructions <span className="text-gray-400 text-xs">(Optional)</span>
                </Label>
                <Textarea
                  id="project-instructions"
                  value={newProjectForm.customInstructions}
                  onChange={(e) => setNewProjectForm(prev => ({ ...prev, customInstructions: e.target.value }))}
                  placeholder="Special instructions for AI responses in this project..."
                  rows={3}
                  className="bg-white/50 backdrop-blur-sm border-gray-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-400/20 rounded-xl px-4 py-3 transition-all resize-none"
                />
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowNewProjectDialog(false)}
                  className="flex-1 bg-white/80 backdrop-blur-sm border-gray-200 rounded-full px-6 py-3 text-gray-700 hover:bg-white/90 hover:shadow-md transition-all duration-300"
                >
                  Cancel
                </Button>
                <Button
                  onClick={createProject}
                  disabled={creatingProject}
                  className="flex-1 bg-gradient-to-r from-highlight to-blue-500 text-white rounded-full px-6 py-3 shadow-sm hover:shadow-md transition-all duration-300 disabled:opacity-50"
                >
                  {creatingProject ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="w-4 h-4 mr-2" />
                      Create Project
                    </>
                  )}
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Signup Encouragement Modal */}
      <Dialog open={showSignupModal} onOpenChange={setShowSignupModal}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center text-2xl font-bold bg-gradient-to-r from-highlight to-blue-500 bg-clip-text text-transparent">
              🎉 You've tried your free prompt!
            </DialogTitle>
            <DialogDescription className="text-center text-gray-600 mt-4">
              Ready to unlock unlimited AI prompt generation? Join thousands of users creating amazing prompts every day.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 mt-6">
            {/* Benefits List */}
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Unlimited prompt generation</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Save prompts to your library</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Chat interface for iterating</span>
              </div>
              <div className="flex items-center gap-3">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-700">Project management tools</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col gap-3 mt-6">
              <Button
                onClick={() => window.open('/auth', '_blank')}
                className="w-full bg-gradient-to-r from-highlight to-blue-500 text-white rounded-full px-6 py-3 shadow-sm hover:shadow-md transition-all duration-300"
              >
                Sign Up & Get Started
              </Button>
              <Button
                onClick={() => window.open('/pricing', '_blank')}
                variant="outline"
                className="w-full rounded-full px-6 py-3"
              >
                View Pricing Plans
              </Button>
              <Button
                onClick={() => setShowSignupModal(false)}
                variant="ghost"
                className="w-full text-gray-500 text-sm"
              >
                Maybe later
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Index;
