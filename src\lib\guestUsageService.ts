import { supabase } from './supabase';
import { toast } from 'sonner';

export interface GuestUsage {
  id: string;
  ip_address: string;
  prompts_used: number;
  first_used_at: string;
  last_used_at: string;
  user_agent: string | null;
  created_at: string;
  updated_at: string;
}

export class GuestUsageService {
  private static eventTarget = new EventTarget();

  /**
   * Listen for guest usage updates
   */
  static addEventListener(callback: () => void) {
    const handler = () => callback();
    this.eventTarget.addEventListener('usageUpdated', handler);
    return () => this.eventTarget.removeEventListener('usageUpdated', handler);
  }

  /**
   * Notify listeners that usage was updated
   */
  private static notifyUsageUpdate() {
    this.eventTarget.dispatchEvent(new CustomEvent('usageUpdated'));
  }

  /**
   * Get the user's IP address from a service
   * Note: In production, you might want to use a more reliable IP detection service
   */
  private static async getUserIP(): Promise<string | null> {
    try {
      // Try to get IP from ipify service with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch('https://api.ipify.org?format=json', {
        signal: controller.signal
      });
      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const data = await response.json();
      return data.ip;
    } catch (error) {
      console.error('Failed to get user IP from ipify:', error);

      // Fallback: try another service
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch('https://httpbin.org/ip', {
          signal: controller.signal
        });
        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        return data.origin.split(',')[0].trim(); // Handle potential proxy IPs
      } catch (fallbackError) {
        console.error('Failed to get user IP from httpbin:', fallbackError);

        // Final fallback: use a hash of user agent + timestamp as pseudo-IP
        // This isn't perfect but allows the feature to work even without IP detection
        const pseudoIP = this.generatePseudoIP();
        console.warn('Using pseudo-IP for guest tracking:', pseudoIP);
        return pseudoIP;
      }
    }
  }

  /**
   * Generate a pseudo-IP based on browser fingerprint
   * This is a fallback when real IP detection fails
   */
  private static generatePseudoIP(): string {
    const userAgent = navigator.userAgent;
    const screen = `${window.screen.width}x${window.screen.height}`;
    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
    const language = navigator.language;

    // Create a simple hash
    const fingerprint = `${userAgent}-${screen}-${timezone}-${language}`;
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
      const char = fingerprint.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }

    // Convert hash to IP-like format
    const a = (hash >>> 24) & 255;
    const b = (hash >>> 16) & 255;
    const c = (hash >>> 8) & 255;
    const d = hash & 255;

    return `10.${Math.abs(a)}.${Math.abs(b)}.${Math.abs(d)}`;
  }

  /**
   * Get user agent string
   */
  private static getUserAgent(): string {
    return navigator.userAgent;
  }

  /**
   * Check if a guest user can use their free prompt
   */
  static async canUseFreeTrial(): Promise<{ canUse: boolean; error: Error | null }> {
    try {
      const userIP = await this.getUserIP();
      
      if (!userIP) {
        return { canUse: false, error: new Error('Unable to determine IP address') };
      }

      // Call the database function to check usage
      const { data, error } = await supabase.rpc('can_guest_use_free_prompt', {
        p_ip_address: userIP
      });

      if (error) {
        console.error('Error checking guest usage:', error);
        return { canUse: false, error: new Error(error.message) };
      }

      return { canUse: data === true, error: null };
    } catch (error) {
      console.error('Error in canUseFreeTrial:', error);
      return { canUse: false, error: error as Error };
    }
  }

  /**
   * Get current guest usage for the user's IP
   */
  static async getCurrentUsage(): Promise<{ usage: GuestUsage | null; error: Error | null }> {
    try {
      const userIP = await this.getUserIP();
      
      if (!userIP) {
        return { usage: null, error: new Error('Unable to determine IP address') };
      }

      const { data, error } = await supabase
        .from('guest_usage')
        .select('*')
        .eq('ip_address', userIP)
        .maybeSingle(); // Use maybeSingle() instead of single()

      if (error) {
        console.error('Error getting guest usage:', error);
        return { usage: null, error: new Error(error.message) };
      }

      return { usage: data || null, error: null };
    } catch (error) {
      console.error('Error in getCurrentUsage:', error);
      return { usage: null, error: error as Error };
    }
  }

  /**
   * Increment the guest usage count
   */
  static async incrementUsage(): Promise<{ success: boolean; usage: GuestUsage | null; error: Error | null }> {
    try {
      const userIP = await this.getUserIP();
      
      if (!userIP) {
        return { success: false, usage: null, error: new Error('Unable to determine IP address') };
      }

      const userAgent = this.getUserAgent();

      // Call the database function to increment usage
      const { data, error } = await supabase.rpc('increment_guest_usage', {
        p_ip_address: userIP,
        p_user_agent: userAgent
      });

      if (error) {
        console.error('Error incrementing guest usage:', error);
        return { success: false, usage: null, error: new Error(error.message) };
      }

      // Notify listeners that usage was updated
      this.notifyUsageUpdate();

      return { success: true, usage: data, error: null };
    } catch (error) {
      console.error('Error in incrementUsage:', error);
      return { success: false, usage: null, error: error as Error };
    }
  }

  /**
   * Check if user has exceeded their free trial limit
   */
  static async hasExceededLimit(): Promise<{ exceeded: boolean; usage: GuestUsage | null; error: Error | null }> {
    try {
      const { usage, error } = await this.getCurrentUsage();
      
      if (error) {
        return { exceeded: false, usage: null, error };
      }

      // If no usage record exists, they haven't used any prompts yet
      if (!usage) {
        return { exceeded: false, usage: null, error: null };
      }

      // Check if they've used their free prompt
      const exceeded = usage.prompts_used >= 1;
      
      return { exceeded, usage, error: null };
    } catch (error) {
      console.error('Error in hasExceededLimit:', error);
      return { exceeded: false, usage: null, error: error as Error };
    }
  }

  /**
   * Get remaining free prompts for the user
   */
  static async getRemainingPrompts(): Promise<{ remaining: number; error: Error | null }> {
    try {
      const { usage, error } = await this.getCurrentUsage();
      
      if (error) {
        return { remaining: 0, error };
      }

      // If no usage record exists, they have 1 free prompt
      if (!usage) {
        return { remaining: 1, error: null };
      }

      const remaining = Math.max(0, 1 - usage.prompts_used);
      return { remaining, error: null };
    } catch (error) {
      console.error('Error in getRemainingPrompts:', error);
      return { remaining: 0, error: error as Error };
    }
  }

  /**
   * Show appropriate error message based on guest usage status
   */
  static showUsageLimitMessage(): void {
    toast.error('Free trial used! Sign up to continue generating prompts.', {
      action: {
        label: 'Sign Up',
        onClick: () => {
          window.location.href = '/auth';
        }
      },
      duration: 5000
    });
  }

  /**
   * Show success message for free trial usage
   */
  static showFreeTrialMessage(): void {
    toast.success('Enjoy your free prompt! Sign up to generate unlimited prompts.', {
      action: {
        label: 'Sign Up',
        onClick: () => {
          window.location.href = '/auth';
        }
      },
      duration: 4000
    });
  }
}



