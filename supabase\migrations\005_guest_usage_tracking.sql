-- Guest usage tracking for free trial prompts
-- This table tracks IP addresses and their prompt usage to allow one free prompt per IP

-- Create guest usage table
CREATE TABLE public.guest_usage (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    ip_address INET NOT NULL,
    prompts_used INTEGER DEFAULT 0 NOT NULL,
    first_used_at TIMESTAMPTZ DEFAULT NOW(),
    last_used_at TIMESTAMPTZ DEFAULT NOW(),
    user_agent TEXT, -- Optional: track browser info for analytics
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create unique index on ip_address for fast lookups and prevent duplicates
CREATE UNIQUE INDEX idx_guest_usage_ip ON public.guest_usage(ip_address);

-- Create index on timestamps for cleanup queries
CREATE INDEX idx_guest_usage_first_used ON public.guest_usage(first_used_at);
CREATE INDEX idx_guest_usage_last_used ON public.guest_usage(last_used_at);

-- Enable RLS (Row Level Security)
ALTER TABLE public.guest_usage ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- Allow anonymous users to read their own usage (by IP)
CREATE POLICY "Allow anonymous users to read own usage" ON public.guest_usage
    FOR SELECT
    TO anon
    USING (ip_address = inet_client_addr());

-- Allow anonymous users to insert their own usage
CREATE POLICY "Allow anonymous users to insert own usage" ON public.guest_usage
    FOR INSERT
    TO anon
    WITH CHECK (ip_address = inet_client_addr());

-- Allow anonymous users to update their own usage
CREATE POLICY "Allow anonymous users to update own usage" ON public.guest_usage
    FOR UPDATE
    TO anon
    USING (ip_address = inet_client_addr())
    WITH CHECK (ip_address = inet_client_addr());

-- Allow authenticated users to read their own usage (in case they were guests before)
CREATE POLICY "Allow authenticated users to read own usage" ON public.guest_usage
    FOR SELECT
    TO authenticated
    USING (ip_address = inet_client_addr());

-- Allow admins to read all guest usage for analytics
CREATE POLICY "Allow admins to read all guest usage" ON public.guest_usage
    FOR SELECT
    TO authenticated
    USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.id = auth.uid()
            AND profiles.subscription_status = 'admin'
        )
    );

-- Create function to get or create guest usage record
CREATE OR REPLACE FUNCTION public.get_or_create_guest_usage(
    p_ip_address INET,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS public.guest_usage AS $$
DECLARE
    usage_record public.guest_usage;
BEGIN
    -- Try to get existing record
    SELECT * INTO usage_record
    FROM public.guest_usage
    WHERE ip_address = p_ip_address;
    
    -- If no record exists, create one
    IF NOT FOUND THEN
        INSERT INTO public.guest_usage (ip_address, user_agent)
        VALUES (p_ip_address, p_user_agent)
        RETURNING * INTO usage_record;
    END IF;
    
    RETURN usage_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to increment guest usage
CREATE OR REPLACE FUNCTION public.increment_guest_usage(
    p_ip_address INET,
    p_user_agent TEXT DEFAULT NULL
)
RETURNS public.guest_usage AS $$
DECLARE
    usage_record public.guest_usage;
BEGIN
    -- Get or create the usage record
    SELECT * INTO usage_record
    FROM public.get_or_create_guest_usage(p_ip_address, p_user_agent);
    
    -- Increment the usage count
    UPDATE public.guest_usage
    SET 
        prompts_used = prompts_used + 1,
        last_used_at = NOW(),
        updated_at = NOW()
    WHERE ip_address = p_ip_address
    RETURNING * INTO usage_record;
    
    RETURN usage_record;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if guest can use free prompt
CREATE OR REPLACE FUNCTION public.can_guest_use_free_prompt(
    p_ip_address INET
)
RETURNS BOOLEAN AS $$
DECLARE
    usage_count INTEGER;
BEGIN
    -- Get current usage count for this IP
    SELECT COALESCE(prompts_used, 0) INTO usage_count
    FROM public.guest_usage
    WHERE ip_address = p_ip_address;
    
    -- If no record found, they haven't used any prompts yet
    IF NOT FOUND THEN
        RETURN TRUE;
    END IF;
    
    -- Allow if they haven't used their free prompt yet
    RETURN usage_count < 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE ON public.guest_usage TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.get_or_create_guest_usage(INET, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.increment_guest_usage(INET, TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION public.can_guest_use_free_prompt(INET) TO anon, authenticated;

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_guest_usage_updated_at
    BEFORE UPDATE ON public.guest_usage
    FOR EACH ROW
    EXECUTE FUNCTION public.update_updated_at_column();
